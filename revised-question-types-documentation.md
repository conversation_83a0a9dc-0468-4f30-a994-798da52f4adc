# Revised Question Types and Special Cases Documentation

## Question Type Classification

Based on comprehensive analysis of all questionnaire components, here are the actual question types found in the system:

```typescript
type QuestionType = 
  | 'radio'           // Single-select radio buttons
  | 'checkbox'        // Multiple checkboxes  
  | 'slider'          // 1-10 scale sliders
  | 'text'            // Text input (non-scoring)
  | 'special_logic'   // Custom scoring logic
  | 'date'            // Date picker
  | 'dropdown';       // Select dropdowns
```

## Questions by Type and Section Across All Questionnaires

### **Radio Button Questions (Standard Scoring Map)**

#### THC Increase Questionnaire (8 Steps/Sections)

**Step 1: Current Usage Patterns**
- `consistency` - How consistently do you use your current medication?
- `dosage` - What is your typical daily dosage?
- `frequency` - How often do you use your medication per day?

**Step 2: Treatment Assessment**
- `effectiveness` - Rate the effectiveness (1-10 scale converted to radio options)

**Step 3: Symptom Management**
- `symptomChanges` - How have your symptoms changed?

**Step 4: Side Effects Assessment**
- `sideEffectManageability` - How manageable are side effects? (1-10 scale)
- `concerns` - Do you have concerns about increasing THC?

**Step 5: Treatment Effectiveness**
- `treatmentEffectiveness` - Overall treatment effectiveness
- `weaknessAssessment` - Is current treatment too weak?

**Step 6: Relief and Satisfaction**
- `insufficientRelief` - Do you experience insufficient relief?
- `satisfactionWithForm` - Satisfaction with current form

**Step 7: Future Treatment Preferences**
- `openToHigherPotency` - Open to higher potency?
- `quickReliefImportance` - Importance of quick relief

**Step 8: Treatment Continuation**
- `continueTreatment` - Likelihood to continue treatment
- `overallSatisfaction` - Overall satisfaction

#### ExtendTP Questionnaire (12 Steps/Sections - One Question Per Step)

**Step 1: Treatment Adherence**
- `adherence` - Treatment plan adherence

**Step 2: Symptom Improvement**
- `symptomImprovement` - Symptom improvement

**Step 3: Symptom Frequency**
- `symptomFrequency` - Symptom frequency changes

**Step 4: Additional Relief Needs**
- `additionalRelief` - Need for additional relief

**Step 5: Functional Benefits**
- `functionalBenefit` - Functional improvement

**Step 6: Sleep Quality**
- `sleepQuality` - Sleep quality changes

**Step 7: Tolerance Development**
- `tolerance` - Tolerance development

**Step 8: Side Effect Severity**
- `sideEffectSeverity` - Side effect severity

**Step 9: Side Effect Impact**
- `sideEffectTolerability` - Side effect impact

**Step 10: Overall Satisfaction**
- `overallSatisfaction` - Overall satisfaction

**Step 11: Goal Achievement**
- `goalAchievement` - Treatment goal achievement

**Step 12: Future Treatment Intent**
- `treatmentIntent` - Future treatment intention

#### Add 22% THC Questionnaire (5 Steps/Sections)

**Step 3: Health Changes Assessment**
- `healthChanges` - Recent health changes

**Step 4: Usage Planning**
- `usagePlan` - How would you use 22% THC?

**Step 5: Consent**
- `consent` - Consent to add 22% THC

#### Quantity Increase Questionnaire (5 Steps/Sections)

**Step 2: Treatment Response Assessment**
- `usageConsistency` - Usage consistency

**Step 3: Health Changes**
- `healthChanges` - Health status changes

**Step 4: Usage Planning**
- `intendedUsage` - How increased quantity will be used

**Step 5: Consent**
- `consent` - Consent to quantity increase

#### Initial Patient Questionnaire
- `condition` - Primary medical condition
- `first_medication` - First medication tried
- `second_medication` - Second medication tried
- `children` - Children status
- `disorder` - Mental health disorders
- `diseases` - Other diseases
- `addiction` - Addiction history
- `treatment` - Previous treatments
- `alternative_medecine` - Alternative medicine use
- `trial` - Clinical trial participation
- `gender` - Gender identity

### **Checkbox Questions (Boolean Scoring)**

#### Add 22% THC Questionnaire

**Step 1: Reasons for Requesting 22% THC**
- `reasonSideEffects` - Experiencing side effects with 29%
- `reasonGentlerEffect` - Want gentler effect
- `reasonDifferentStrain` - Want different strain option
- `reasonTolerance` - Tolerance to current strength
- `reasonOther` - Other reason

**Step 2: Current Side Effects Assessment**
- `sideEffectsNone` - No side effects
- `sideEffectsMild` - Mild side effects
- `sideEffectsModerate` - Moderate side effects
- `sideEffectsStrong` - Strong side effects

#### Quantity Increase Questionnaire

**Step 1: Reasons for Quantity Increase**
- `reasonNotLasting` - Current amount not lasting
- `reasonHigherDoses` - Need higher doses for effect
- `reasonTolerance` - Developed tolerance
- `reasonIncreasedSymptoms` - Symptoms have increased
- `reasonOther` - Other reason

**Step 2: Side Effects Assessment**
- Various side effect checkboxes (severity levels)

### **Slider Questions (1-10 Scale)**

#### Add 22% THC Questionnaire

**Step 2: Current Treatment Response**
- `symptomImprovement` - Symptom improvement rating (1-10 scale)

#### Quantity Increase Questionnaire

**Step 2: Treatment Effectiveness Assessment**
- `currentEffectiveness` - Current treatment effectiveness (1-10 scale)

### **Text Input Questions (Non-scoring)**

#### THC Increase Questionnaire

**Step 2: Condition Details**
- `conditionOther` - Text field for "other" condition description

**Step 3: Side Effects Details**
- `sideEffectsOther` - Text field for "other" side effects description

#### Add 22% THC Questionnaire

**Step 1: Additional Reason Details**
- `reasonOtherText` - Text description for "other" reason

**Step 2: Side Effects Details**
- `sideEffectsDescription` - Description of side effects

**Step 3: Health Changes Details**
- `healthChangesDescription` - Description of health changes

**Step 4: Patient Input**
- `expectations` - Patient expectations (text)
- `concerns` - Patient concerns (text)

#### Quantity Increase Questionnaire

**Step 4: Patient Input**
- `expectations` - Patient expectations (text field)
- `concerns` - Patient concerns (text field)

### **Special Logic Questions (Custom Scoring)**

#### THC Increase Questionnaire

**Step 2: `condition` - Primary condition being treated**
```typescript
// Special Logic: Any condition except 'other' gets 2 points
if (questionKey === 'condition') {
    return value === 'other' ? 0 : 2;
}
```
- **Logic**: Non-'other' conditions = 2 points, 'other' = 0 points
- **Rationale**: Validates legitimate medical conditions
- **Section**: Treatment Assessment

**Step 3: `sideEffect` - Current side effects**
```typescript
// Special Logic: 'none' = 4 points, specific effects = 1 point, 'other' = 0
if (questionKey === 'sideEffect') {
    if (value === 'none') return 4;
    if (value === 'other') return 0;
    return 1; // Any specific side effect
}
```
- **Logic**: No side effects = 4 points, known side effects = 1 point, other = 0 points
- **Rationale**: Rewards patients with no side effects, acknowledges known effects
- **Section**: Symptom Management

### **Date Questions**

#### Initial Patient Questionnaire
- `dob` - Date of birth (Age validation: must be 18+)

### **Dropdown Questions**

#### Initial Patient Questionnaire
- All questions use dropdown/select interfaces but function like radio buttons
- No special dropdown-specific logic identified

## Sectional Structure Summary

### **Section/Step Organization by Questionnaire**

| Questionnaire | Total Steps | Structure | Navigation |
|--------------|-------------|-----------|------------|
| THC Increase | 8 steps | Multi-question steps | Step-by-step with validation |
| ExtendTP | 12 steps | One question per step | Linear progression |
| Add 22% THC | 5 steps | Mixed question types per step | Step-by-step with validation |
| Quantity Increase | 5 steps | Complex first step, then single questions | Step-by-step with validation |
| Initial Questionnaire | Single page | All questions on one page | No step navigation |

### **Section Themes by Questionnaire**

#### THC Increase (Treatment Upgrade Assessment)
1. **Current Usage Patterns** - Baseline usage assessment
2. **Treatment Assessment** - Effectiveness and condition evaluation
3. **Symptom Management** - Symptom changes and side effects
4. **Side Effects Assessment** - Manageability and concerns
5. **Treatment Effectiveness** - Overall effectiveness evaluation
6. **Relief and Satisfaction** - Current treatment satisfaction
7. **Future Treatment Preferences** - Openness to changes
8. **Treatment Continuation** - Long-term treatment intent

#### ExtendTP (Treatment Extension Evaluation)
- Each step focuses on a single aspect of treatment evaluation
- Linear progression through treatment domains
- Comprehensive assessment of current treatment success

#### Add 22% THC (Additional Option Assessment)
1. **Reason Assessment** - Why patient wants 22% option
2. **Current Response** - How patient responds to 29% THC
3. **Health Status** - Recent health changes
4. **Usage Planning** - How 22% would be used
5. **Consent** - Final approval for addition

#### Quantity Increase (Dosage Increase Evaluation)
1. **Comprehensive Request** - Reasons + strength selection + quantities
2. **Treatment Response** - Current effectiveness assessment
3. **Health Changes** - Recent health status changes
4. **Usage Planning** - Expectations and intended usage
5. **Consent** - Final approval for increase

## Admin Interface Implications

### **Section-Based Admin Interface Design**

#### **Questionnaire Section Manager**
- **Section Overview**: Display all sections with question counts and total possible points
- **Section Reordering**: Drag-and-drop section reordering capability
- **Section Themes**: Editable section titles and descriptions
- **Section Validation**: Ensure logical flow between sections
- **Section Analytics**: Show completion rates and drop-off points per section

#### **Section-Specific Editing**
- **Grouped Question Editing**: Edit all questions within a section simultaneously
- **Section Scoring**: View total points possible per section
- **Section Dependencies**: Manage questions that depend on previous section answers
- **Section Validation Rules**: Set requirements for section completion

#### **Multi-Step Navigation Logic**
- **Step Validation Rules**: Configure what makes each step "complete"
- **Conditional Navigation**: Set up skip logic based on previous answers
- **Progress Tracking**: Configure progress bar behavior and milestones
- **Section Timing**: Optional time limits or recommendations per section

### **Question Type Handling in Admin Panel**

#### **Radio Button Editor**
- Standard option list with score inputs
- Validation for score ranges
- Preview of score distribution

#### **Checkbox Editor**
- Boolean true/false scoring inputs
- Multiple selection impact calculator
- Combination scoring preview

#### **Slider Editor**
- Range mapping interface (1-10 to point values)
- Curve adjustment tools
- Visual score distribution

#### **Text Field Manager**
- Mark as non-scoring (0 points)
- Set character limits and validation rules
- Define purpose for medical review

#### **Special Logic Editor**
- **Read-only Logic Display**: Show current logic with explanation
- **Test Interface**: Input test values to see scoring results
- **Developer Flag**: Mark as requiring code changes
- **Warning System**: Alert that changes need technical implementation

```typescript
interface SpecialLogicConfig {
  questionKey: string;
  description: string;
  codeSnippet: string;
  testCases: Array<{
    input: string;
    expectedScore: number;
    description: string;
  }>;
  requiresDeveloper: boolean;
  lastModified: Date;
}
```

#### **Date Field Editor**
- Validation rule settings (min/max age)
- Format specifications
- Required/optional toggle

### **Updated Data Structure with Section Support**

```typescript
interface QuestionnaireConfig {
  id: string;
  name: string;
  maxScore: number;
  threshold: number;
  isActive: boolean;
  version: string;
  sections: SectionConfig[];
  lastModified: Date;
  modifiedBy: string;
}

interface SectionConfig {
  id: string;
  title: string;
  description?: string;
  order: number;
  questions: QuestionConfig[];
  validationRules: SectionValidationRules;
  isActive: boolean;
}

interface SectionValidationRules {
  requireAllQuestions: boolean;
  minimumQuestionsRequired?: number;
  customValidationLogic?: string;
}

interface QuestionConfig {
  key: string;
  text: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'special_logic' | 'date' | 'dropdown';
  sectionId: string; // Links question to its section

  // Standard scoring (radio, checkbox, dropdown)
  answerOptions?: AnswerOption[];

  // Slider-specific
  sliderConfig?: {
    min: number;
    max: number;
    scoreMapping: Record<string, number>;
  };

  // Text field configuration
  textFieldConfig?: {
    maxLength: number;
    required: boolean;
    placeholder: string;
    purpose: string;
  };

  // Special logic configuration
  specialLogic?: SpecialLogicConfig;

  // Date field configuration
  dateConfig?: {
    minAge?: number;
    maxAge?: number;
    required: boolean;
  };

  order: number;
  contributesToScore: boolean;
  isActive: boolean;
  dependsOnQuestion?: string; // For conditional questions
}
```

### **Admin Interface Warnings**

#### **Special Logic Questions**
- **Warning Badge**: "⚠️ Requires Developer"
- **Explanation**: "This question uses custom scoring logic that cannot be modified through the admin interface"
- **Action Options**: "Request Developer Change" button
- **Test Mode**: Allow testing current logic with different inputs

#### **Text Fields**
- **Info Badge**: "ℹ️ Non-scoring"
- **Purpose Display**: Show what the text is used for
- **Character Count**: Live character limit feedback

#### **Dependent Questions**
- **Dependency Warning**: Show which questions depend on others
- **Impact Analysis**: Show how changes affect dependent questions

This revised documentation provides a complete picture of all question types and their special handling requirements for the admin interface implementation.
