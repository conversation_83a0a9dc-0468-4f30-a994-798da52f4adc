# Revised Question Types and Special Cases Documentation

## Question Type Classification

Based on comprehensive analysis of all questionnaire components, here are the actual question types found in the system:

```typescript
type QuestionType = 
  | 'radio'           // Single-select radio buttons
  | 'checkbox'        // Multiple checkboxes  
  | 'slider'          // 1-10 scale sliders
  | 'text'            // Text input (non-scoring)
  | 'special_logic'   // Custom scoring logic
  | 'date'            // Date picker
  | 'dropdown';       // Select dropdowns
```

## Questions by Type Across All Questionnaires

### **Radio Button Questions (Standard Scoring Map)**

#### THC Increase Questionnaire
- `consistency` - How consistently do you use your current medication?
- `dosage` - What is your typical daily dosage?
- `frequency` - How often do you use your medication per day?
- `effectiveness` - Rate the effectiveness (1-10 scale converted to radio options)
- `symptomChanges` - How have your symptoms changed?
- `sideEffectManageability` - How manageable are side effects? (1-10 scale)
- `concerns` - Do you have concerns about increasing THC?
- `treatmentEffectiveness` - Overall treatment effectiveness
- `weaknessAssessment` - Is current treatment too weak?
- `insufficientRelief` - Do you experience insufficient relief?
- `satisfactionWithForm` - Satisfaction with current form
- `openToHigherPotency` - Open to higher potency?
- `quickReliefImportance` - Importance of quick relief
- `continueTreatment` - Likelihood to continue treatment
- `overallSatisfaction` - Overall satisfaction

#### ExtendTP Questionnaire
- `adherence` - Treatment plan adherence
- `symptomImprovement` - Symptom improvement
- `symptomFrequency` - Symptom frequency changes
- `additionalRelief` - Need for additional relief
- `functionalBenefit` - Functional improvement
- `sleepQuality` - Sleep quality changes
- `tolerance` - Tolerance development
- `sideEffectSeverity` - Side effect severity
- `sideEffectTolerability` - Side effect impact
- `overallSatisfaction` - Overall satisfaction
- `goalAchievement` - Treatment goal achievement
- `treatmentIntent` - Future treatment intention

#### Add 22% THC Questionnaire
- `healthChanges` - Recent health changes
- `usagePlan` - How would you use 22% THC?
- `consent` - Consent to add 22% THC

#### Quantity Increase Questionnaire
- `usageConsistency` - Usage consistency
- `healthChanges` - Health status changes
- `intendedUsage` - How increased quantity will be used
- `consent` - Consent to quantity increase

#### Initial Patient Questionnaire
- `condition` - Primary medical condition
- `first_medication` - First medication tried
- `second_medication` - Second medication tried
- `children` - Children status
- `disorder` - Mental health disorders
- `diseases` - Other diseases
- `addiction` - Addiction history
- `treatment` - Previous treatments
- `alternative_medecine` - Alternative medicine use
- `trial` - Clinical trial participation
- `gender` - Gender identity

### **Checkbox Questions (Boolean Scoring)**

#### Add 22% THC Questionnaire
- `reasonSideEffects` - Experiencing side effects with 29%
- `reasonGentlerEffect` - Want gentler effect
- `reasonDifferentStrain` - Want different strain option
- `reasonTolerance` - Tolerance to current strength
- `reasonOther` - Other reason
- `sideEffectsNone` - No side effects
- `sideEffectsMild` - Mild side effects
- `sideEffectsModerate` - Moderate side effects
- `sideEffectsStrong` - Strong side effects

#### Quantity Increase Questionnaire
- `reasonNotLasting` - Current amount not lasting
- `reasonHigherDoses` - Need higher doses for effect
- `reasonTolerance` - Developed tolerance
- `reasonIncreasedSymptoms` - Symptoms have increased
- `reasonOther` - Other reason
- Various side effect checkboxes (severity levels)

### **Slider Questions (1-10 Scale)**

#### Add 22% THC Questionnaire
- `symptomImprovement` - Symptom improvement rating (1-10 scale)

#### Quantity Increase Questionnaire
- `currentEffectiveness` - Current treatment effectiveness (1-10 scale)

### **Text Input Questions (Non-scoring)**

#### THC Increase Questionnaire
- `conditionOther` - Text field for "other" condition description
- `sideEffectsOther` - Text field for "other" side effects description

#### Add 22% THC Questionnaire
- `reasonOtherText` - Text description for "other" reason
- `sideEffectsDescription` - Description of side effects
- `healthChangesDescription` - Description of health changes
- `expectations` - Patient expectations (text)
- `concerns` - Patient concerns (text)

#### Quantity Increase Questionnaire
- `expectations` - Patient expectations (text field)
- `concerns` - Patient concerns (text field)

### **Special Logic Questions (Custom Scoring)**

#### THC Increase Questionnaire

**`condition` - Primary condition being treated**
```typescript
// Special Logic: Any condition except 'other' gets 2 points
if (questionKey === 'condition') {
    return value === 'other' ? 0 : 2;
}
```
- **Logic**: Non-'other' conditions = 2 points, 'other' = 0 points
- **Rationale**: Validates legitimate medical conditions

**`sideEffect` - Current side effects**
```typescript
// Special Logic: 'none' = 4 points, specific effects = 1 point, 'other' = 0
if (questionKey === 'sideEffect') {
    if (value === 'none') return 4;
    if (value === 'other') return 0;
    return 1; // Any specific side effect
}
```
- **Logic**: No side effects = 4 points, known side effects = 1 point, other = 0 points
- **Rationale**: Rewards patients with no side effects, acknowledges known effects

### **Date Questions**

#### Initial Patient Questionnaire
- `dob` - Date of birth (Age validation: must be 18+)

### **Dropdown Questions**

#### Initial Patient Questionnaire
- All questions use dropdown/select interfaces but function like radio buttons
- No special dropdown-specific logic identified

## Admin Interface Implications

### **Question Type Handling in Admin Panel**

#### **Radio Button Editor**
- Standard option list with score inputs
- Validation for score ranges
- Preview of score distribution

#### **Checkbox Editor**
- Boolean true/false scoring inputs
- Multiple selection impact calculator
- Combination scoring preview

#### **Slider Editor**
- Range mapping interface (1-10 to point values)
- Curve adjustment tools
- Visual score distribution

#### **Text Field Manager**
- Mark as non-scoring (0 points)
- Set character limits and validation rules
- Define purpose for medical review

#### **Special Logic Editor**
- **Read-only Logic Display**: Show current logic with explanation
- **Test Interface**: Input test values to see scoring results
- **Developer Flag**: Mark as requiring code changes
- **Warning System**: Alert that changes need technical implementation

```typescript
interface SpecialLogicConfig {
  questionKey: string;
  description: string;
  codeSnippet: string;
  testCases: Array<{
    input: string;
    expectedScore: number;
    description: string;
  }>;
  requiresDeveloper: boolean;
  lastModified: Date;
}
```

#### **Date Field Editor**
- Validation rule settings (min/max age)
- Format specifications
- Required/optional toggle

### **Updated Data Structure**

```typescript
interface QuestionConfig {
  key: string;
  text: string;
  type: 'radio' | 'checkbox' | 'slider' | 'text' | 'special_logic' | 'date' | 'dropdown';
  
  // Standard scoring (radio, checkbox, dropdown)
  answerOptions?: AnswerOption[];
  
  // Slider-specific
  sliderConfig?: {
    min: number;
    max: number;
    scoreMapping: Record<string, number>;
  };
  
  // Text field configuration
  textFieldConfig?: {
    maxLength: number;
    required: boolean;
    placeholder: string;
    purpose: string;
  };
  
  // Special logic configuration
  specialLogic?: SpecialLogicConfig;
  
  // Date field configuration
  dateConfig?: {
    minAge?: number;
    maxAge?: number;
    required: boolean;
  };
  
  order: number;
  contributesToScore: boolean;
  isActive: boolean;
}
```

### **Admin Interface Warnings**

#### **Special Logic Questions**
- **Warning Badge**: "⚠️ Requires Developer"
- **Explanation**: "This question uses custom scoring logic that cannot be modified through the admin interface"
- **Action Options**: "Request Developer Change" button
- **Test Mode**: Allow testing current logic with different inputs

#### **Text Fields**
- **Info Badge**: "ℹ️ Non-scoring"
- **Purpose Display**: Show what the text is used for
- **Character Count**: Live character limit feedback

#### **Dependent Questions**
- **Dependency Warning**: Show which questions depend on others
- **Impact Analysis**: Show how changes affect dependent questions

This revised documentation provides a complete picture of all question types and their special handling requirements for the admin interface implementation.
