import moment from "moment";

export type RegistrationData = {
	firstname: string;
	lastname: string;
	fullName?: string;
	email: string;
	password_confirm: string;
	password: string;
	phone: string;
};

export type AuthUser = {
	firstname: string;
	lastname: string;
	fullName?: string;
	email: string;
	password: string;
	phone: string;
	phoneverified: boolean | undefined;
	laststep: string;
	status: string;
};

export type Questionnaire = {
	dob: moment.Moment | null;
	condition: string;
	first_medication: string;
	second_medication: string;
	children: string;
	disorder: string;
	diseases: string;
	alternative_medecine: string;
	addiction: string;
	treatment: string;
	trial: string;
	gender: string;
};

export type TreatmentPlan = {
	consultingDoctor: string;
	treatmentPlanStartDate: string;
	treatmentPlanEndDate: string;
	thcContent: string;
	totalAllowance: {
		thc22: string;
		thc29: string;
	};
	totalAllowanceUsed: {
		thc22: string;
		thc29: string;
	};
	repeatAllowance: {
		thc22: string;
		thc29: string;
	};
	numberOfRepeats: number;
	repeatsRemaining: {
		thc22: number;
		thc29: number;
	};
	nextRepeatDate: {
		thc22: string;
		thc29: string;
	};
	supplyRemainingForRepeat: {
		thc22: string;
		thc29: string;
	};
};

export type TreatmentPlanResponse = {
	success: boolean;
	email: string;
	contactId: string;
	fullName: string;
	treatmentPlan: TreatmentPlan;
	additionalInfo: {
		memberStatus: string;
		memberID: string;
		doctorNotes: string | null;
		mentalHealthSupportingDocumentation: string;
		dispensingInterval: string;
		maximumDosesPerDay: {
			thc22: string | null;
			thc29: string | null;
		};
		pouchCount: {
			thc22: string | null;
			thc29: string | null;
		};
		lastModified: string;
		createdTime: string;
	};
};

// 22% THC Addition Questionnaire Types
export interface Add22ThcFormData {
	// Question 1: Reason for requesting 22% THC
	reasonSideEffects: boolean;
	reasonGentlerEffect: boolean;
	reasonDifferentStrain: boolean;
	reasonTolerance: boolean;
	reasonOther: boolean;
	reasonOtherText: string;

	// Question 2: Current response to 29% THC
	symptomImprovement: string; // 1-10 scale
	sideEffectsNone: boolean;
	sideEffectsMild: boolean;
	sideEffectsModerate: boolean;
	sideEffectsStrong: boolean;
	sideEffectsDescription: string;

	// Question 3: Health changes
	healthChanges: string; // 'no-changes' | 'yes'
	healthChangesDescription: string;

	// Question 4: Expectations and preferences
	expectations: string; // text field
	concerns: string; // text field
	usagePlan: string; // radio buttons

	// Question 5: Consent
	consent: string; // 'yes' | 'no'
}

export interface Add22ThcScoringState {
	totalScore: number;
	maxScore: number;
	isEligible: boolean;
	questionScores: Record<string, number>;
}

export interface Add22ThcQuestionnaireStatus {
	completed: boolean;
	score: number;
	isEligible: boolean;
	status: string; // 'submitted' | 'under_review' | 'approved' | 'rejected'
}

export interface Add22ThcSubmissionData {
	questionsAndAnswers: Array<{
		questionKey: string;
		questionText: string;
		answerValue: string | boolean;
		answerText: string;
		score: number;
	}>;
	totalScore: number;
	maxScore: number;
	isEligible: boolean;
	submittedAt: string;
}
