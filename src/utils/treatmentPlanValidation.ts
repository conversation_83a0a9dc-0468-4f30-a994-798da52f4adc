import { TreatmentPlan } from '../types';

/**
 * Checks if a patient has an active 29% THC treatment plan only (not both 22% and 29%)
 * This is used to determine eligibility for the 22% THC addition questionnaire
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns boolean - true if patient has only 29% THC treatment plan
 */
export function hasOnly29ThcTreatmentPlan(treatmentPlan: TreatmentPlan | null): boolean {
  if (!treatmentPlan) {
    return false;
  }

  // Check if patient has 29% THC allowance
  const hasThc29 = !!(treatmentPlan.totalAllowance?.thc29 &&
                      treatmentPlan.totalAllowance.thc29 !== "0" &&
                      treatmentPlan.totalAllowance.thc29 !== "");

  // Check if patient has 22% THC allowance
  const hasThc22 = !!(treatmentPlan.totalAllowance?.thc22 &&
                      treatmentPlan.totalAllowance.thc22 !== "0" &&
                      treatmentPlan.totalAllowance.thc22 !== "");

  // Patient should have 29% but NOT 22% to be eligible for adding 22%
  return hasThc29 && !hasThc22;
}

/**
 * Checks if a patient has both 22% and 29% THC treatment plans
 * This indicates they already have the dual option setup
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns boolean - true if patient has both 22% and 29% THC treatment plans
 */
export function hasBothThcTreatmentPlans(treatmentPlan: TreatmentPlan | null): boolean {
  if (!treatmentPlan) {
    return false;
  }

  const hasThc29 = !!(treatmentPlan.totalAllowance?.thc29 &&
                      treatmentPlan.totalAllowance.thc29 !== "0" &&
                      treatmentPlan.totalAllowance.thc29 !== "");

  const hasThc22 = !!(treatmentPlan.totalAllowance?.thc22 &&
                      treatmentPlan.totalAllowance.thc22 !== "0" &&
                      treatmentPlan.totalAllowance.thc22 !== "");

  return hasThc29 && hasThc22;
}

/**
 * Checks if a patient has only 22% THC treatment plan
 * These patients would be eligible for THC increase questionnaire instead
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns boolean - true if patient has only 22% THC treatment plan
 */
export function hasOnly22ThcTreatmentPlan(treatmentPlan: TreatmentPlan | null): boolean {
  if (!treatmentPlan) {
    return false;
  }

  const hasThc29 = !!(treatmentPlan.totalAllowance?.thc29 &&
                      treatmentPlan.totalAllowance.thc29 !== "0" &&
                      treatmentPlan.totalAllowance.thc29 !== "");

  const hasThc22 = !!(treatmentPlan.totalAllowance?.thc22 &&
                      treatmentPlan.totalAllowance.thc22 !== "0" &&
                      treatmentPlan.totalAllowance.thc22 !== "");

  return hasThc22 && !hasThc29;
}

/**
 * Checks if a treatment plan is currently active based on dates
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns boolean - true if treatment plan is currently active
 */
export function isTreatmentPlanActive(treatmentPlan: TreatmentPlan | null): boolean {
  if (!treatmentPlan || !treatmentPlan.treatmentPlanStartDate || !treatmentPlan.treatmentPlanEndDate) {
    return false;
  }

  // Get current date in Sydney timezone
  const nowSydney = new Date(new Date().toLocaleString("en-US", {timeZone: "Australia/Sydney"}));

  // Parse treatment plan dates (these are typically in YYYY-MM-DD format)
  const startDate = new Date(treatmentPlan.treatmentPlanStartDate + "T00:00:00");
  const endDate = new Date(treatmentPlan.treatmentPlanEndDate + "T23:59:59");

  return nowSydney >= startDate && nowSydney <= endDate;
}

/**
 * Comprehensive check for 22% THC addition questionnaire eligibility
 * Patient must have:
 * 1. An active treatment plan
 * 2. Only 29% THC (not both 22% and 29%)
 * 3. Valid treatment plan dates
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns boolean - true if patient is eligible for 22% THC addition questionnaire
 */
export function isEligibleForAdd22ThcQuestionnaire(treatmentPlan: TreatmentPlan | null): boolean {
  return isTreatmentPlanActive(treatmentPlan) && hasOnly29ThcTreatmentPlan(treatmentPlan);
}

/**
 * Gets a human-readable description of the patient's current THC treatment plan status
 * 
 * @param treatmentPlan - The patient's treatment plan data
 * @returns string - Description of current THC treatment plan status
 */
export function getTreatmentPlanThcStatus(treatmentPlan: TreatmentPlan | null): string {
  if (!treatmentPlan) {
    return "No treatment plan found";
  }

  const hasThc29 = !!(treatmentPlan.totalAllowance?.thc29 &&
                      treatmentPlan.totalAllowance.thc29 !== "0" &&
                      treatmentPlan.totalAllowance.thc29 !== "");

  const hasThc22 = !!(treatmentPlan.totalAllowance?.thc22 &&
                      treatmentPlan.totalAllowance.thc22 !== "0" &&
                      treatmentPlan.totalAllowance.thc22 !== "");

  if (hasThc29 && hasThc22) {
    return "22% and 29% THC";
  } else if (hasThc29) {
    return "29% THC only";
  } else if (hasThc22) {
    return "22% THC only";
  } else {
    return "No THC treatment plan";
  }
}
