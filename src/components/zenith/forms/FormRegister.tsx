import { useEffect, useState } from "react";
import {
	MobileStepper,
	Box,
	Button,
	Stack,
	TextField,
	Typography,
	Divider,
	Checkbox,
	FormControlLabel,
	ThemeProvider,
	InputAdornment,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import axios from "axios";
import { RegistrationData } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import { useFlow } from "../../../hooks/flow-controller";
import { getStoredUTM } from "../../../utils/getStoredUTM";

const inputStyle = {
	backgroundColor: "white",
	borderRadius: "13px",
	"& .MuiOutlinedInput-root": {
		borderRadius: "13px",
		"&:hover": {
			borderColor: "black",
		},
		"&.Mui-focused fieldset": {
			borderColor: "black",
			borderRadius: "13px",
		},
	},
	"& .MuiInputLabel-root": {
		color: "#3B3B3B",
	},
	"& .MuiInputLabel-root.Mui-focused": {
		color: "white",
	},
};

function FormRegister() {
	const [canSubmit, setCanSubmit] = useState(false);
	const [dataSubmitted, setDataSubmitted] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [isPhoneExists, setIsPhoneExists] = useState(false);
	const [isEmailExists, setIsEmailExists] = useState(false);
	const [terms, setTerms] = useState(false);
	const [emailIsMatching, setEmailIsMatching] = useState(false);
	const [passwordIsMatching, setPasswordIsMatching] = useState(false);
	const navigate = useNavigate();
	const { enqueueSnackbar } = useSnackbar();
	const { loginWithLeadId } = useFlow();
	const [ipAddress, setIpAddress] = useState("");

	const [formData, setFormData] = useState<RegistrationData>({
		firstname: "",
		lastname: "",
		email: "",
		password: "",
		phone: "",
		password_confirm: "",
	});
	const [formErrors, setFormErrors] = useState({
		firstname: false,
		lastname: false,
		email: false,
		password: false,
		password_confirm: false,
		phone: false,
	});

	// Initialize phone with "04" if it's empty
	useEffect(() => {
		if (formData.phone === "") {
			setFormData((prev) => ({
				...prev,
				phone: "",
			}));
		}
	}, []);
	useEffect(() => {
		setPasswordIsMatching(formData.password_confirm == formData.password);
	}, [formData.password_confirm]);
	useEffect(() => {
		if (formData.lastname.length > 0) handleChangeAutoCompletion("lastname", formData.lastname);
	}, [formData.lastname]);
	useEffect(() => {
		if (formData.firstname.length > 0) handleChangeAutoCompletion("firstname", formData.firstname);
	}, [formData.firstname]);
	useEffect(() => {
		if (formData.email.length > 0) handleChangeAutoCompletion("email", formData.email);
	}, [formData.email]);

	const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = event.target;

		// Special handling for phone field
		if (name === "phone") {
			// Remove non-numeric characters
			let phoneValue = value.replace(/[^0-9]/g, "");

			// Limit to 8 digits (excluding the prefix)
			phoneValue = phoneValue.substring(0, 8);

			setFormData((prev) => {
				return { ...prev, [name]: phoneValue };
			});
		} else {
			// Regular handling for other fields
			if (value.length == 0 || value.trim().length > 0)
				setFormData((prev) => {
					return { ...prev, [name]: value };
				});
		}

		let errors = formErrors;
		if (value.length == 0 || value.trim().length > 0) {
			errors = { ...errors, [name]: false };
			setFormErrors({ ...formErrors, [name]: false });
		} else {
			errors = { ...errors, [name]: true };
			setFormErrors({ ...formErrors, [name]: true });
		}
		const errorCheck: boolean[] = Object.entries(errors).map(([key, value]) => value);
		setCanSubmit(
			errorCheck.every(
				(v) =>
					v === false &&
					Object.entries(formData)
						.map(([key, value]) => value)
						.every((v) => v.length > 0)
			)
		);
	};

	const handleChangeAutoCompletion = (name: string, value: String) => {
		setFormData((prev) => {
			return { ...prev, [name]: value };
		});

		let errors = formErrors;
		if (value.length > 0) {
			errors = { ...errors, [name]: false };
			setFormErrors({ ...formErrors, [name]: false });
		} else {
			errors = { ...errors, [name]: true };
			setFormErrors({ ...formErrors, [name]: true });
		}
		const errorCheck: boolean[] = Object.entries(errors).map(([key, value]) => value);
		setCanSubmit(
			errorCheck.every(
				(v) =>
					v === false &&
					Object.entries(formData)
						.map(([key, value]) => value)
						.every((v) => v.length > 0)
			)
		);
	};

	// Effect to fetch the user's public IP address
	useEffect(() => {
		const fetchIpAddress = async () => {
			// Try multiple services for redundancy
			const ipServices = [
				"https://api.ipify.org?format=json", // Falls back to free tier with limited data
				"https://api.ip.sb/jsonip",
			];

			for (const service of ipServices) {
				try {
					const response = await axios.get(service, { timeout: 5000 }); // 5 second timeout

					// Different APIs return IP in different formats
					const ip = response.data.ip || response.data.ipAddress;

					if (ip && /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(ip)) {
						setIpAddress(ip);
						return; // Exit once we have a valid IP
					}
				} catch (error) {
					// Continue to next service
				}
			}

			// If all services fail, try to get IP from the server side
			try {
				const response = await axiosInstance.get("/funnel/v1.0/patient/get-client-ip");
				if (response.data && response.data.ip) {
					setIpAddress(response.data.ip);
					return;
				}
			} catch (error) {
				console.error("Error fetching IP from server:", error);
			}

			// If all methods fail
			setIpAddress("Could not determine IP");
		};

		fetchIpAddress();
	}, []);

	const goToTCs = () => {
		window.open("/patient/terms-conditions", "_blank", "noopener,noreferrer");
	};

	const handleSubmit = async () => {
		setIsLoading(true);
		setIsEmailExists(false);
		setIsPhoneExists(false);

		try {
			// Add IP address to the form data
			const formDataWithIp = {
				...formData,
				phone: "04" + formData.phone, // Add the prefix to the phone number
				user_ip: ipAddress,
			};

			const result = await axiosInstance.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/register`,
				formDataWithIp
			);
			if (result.data.success) {
				setDataSubmitted(true);
				const leadId = result.data.data.leadID;
				try {
					const storedUTM = getStoredUTM();
					if (Object.keys(storedUTM).length > 0) {
						axiosInstance.post(
							`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/storeUtmParams/${leadId}`,
							storedUTM
						);
					}
				} catch (error: any) {
					console.error(`Failed to submit UTM params`, error);
				}
				// add delay to ensure UTM params are recorded
				const timer = setTimeout(async () => {
					await loginWithLeadId(leadId);
				}, 1000);
				
				enqueueSnackbar("You have successfully created your Zenith Clinics account.", {
					variant: "success",
				});
			} else {
				enqueueSnackbar(result.data.message, {
					variant: "error",
				});
			}
		} catch (e: any) {
			console.log(e);
			setDataSubmitted(false);
			if (e.response.status == 400 && e.response.data.type) {
				if (e.response.data.type == "email") {
					setIsEmailExists(true);
				}
				if (e.response.data.type == "phone") {
					setIsPhoneExists(true);
				}
			}
			enqueueSnackbar("The registration process has failed", {
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};
	const steps = [
		{
			label: "Name",
			content: (
				<Stack sx={{ p: 2, pb: 3 }} spacing={2}>
					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								First Name*
							</Typography>
						</Grid>
						<TextField
							sx={{ m: 0, ...inputStyle }}
							type="text"
							name="firstname"
							size="small"
							//autoComplete="off"
							onChange={handleChange}
							slotProps={{
								input: {
									onAnimationStart: (event: any) => {
										if (event.animationName === "mui-auto-fill") {
											setFormData((prev) => ({
												...prev,
												firstname: event.target.value,
											}));
										}
									},
								},
							}}
							margin="normal"
							fullWidth
							value={formData.firstname}
							required={true}
							error={formErrors.firstname}
							helperText={formErrors.firstname ? "Please enter your First name" : ""}
						/>
					</Grid>
					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								Last Name*
							</Typography>
						</Grid>
						<TextField
							type="text"
							sx={{ m: 0, ...inputStyle }}
							size="small"
							name="lastname"
							fullWidth
							//autoComplete="off"
							onChange={handleChange}
							margin="normal"
							slotProps={{
								input: {
									onAnimationStart: (event: any) => {
										if (event.animationName === "mui-auto-fill") {
											setFormData((prev) => ({
												...prev,
												lastname: event.target.value,
											}));
										}
									},
								},
							}}
							value={formData.lastname}
							required={true}
							error={formErrors.lastname}
							helperText={formErrors.lastname ? "Please enter your Last name" : ""}
						/>
					</Grid>
					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								Email*
							</Typography>
						</Grid>
						<TextField
							sx={{ m: 0, ...inputStyle }}
							type="email"
							size="small"
							name="email"
							//autoComplete="off"
							onChange={handleChange}
							margin="normal"
							slotProps={{
								input: {
									onAnimationStart: (event: any) => {
										if (event.animationName === "mui-auto-fill") {
											setFormData((prev) => ({
												...prev,
												email: event.target.value,
											}));
										}
									},
								},
							}}
							fullWidth
							value={formData.email}
							required={true}
							onKeyPress={(event) => {
								if (event.key === " ") {
									event.preventDefault();
								}
							}}
							error={formErrors.email}
							helperText={formErrors.email ? "Please enter a valid email" : ""}
						/>
					</Grid>
					{isEmailExists && (
						<Grid container direction={"column"} alignItems={"start"}>
							<Button
								sx={{
									textTransform: "none",
									color: "white",
									float: "left",
									borderBottomStyle: "solid",
									borderBottomWidth: "1px",
									borderBottomColor: "white",
									fontSize: "12px",
									padding: "0px 5px",
									textAlign: "start",
								}}
								onClick={() => navigate({ to: "/patient/login" })}
							>
								This email already exists.
								<i
									style={{
										paddingLeft: "1px",
										color: "black",
										cursor: "pointer",
									}}
								>
									Click here to login
								</i>
							</Button>
						</Grid>
					)}

					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								Phone*
							</Typography>
						</Grid>
						<TextField
							sx={{ m: 0, ...inputStyle }}
							type="tel"
							size="small"
							autoComplete="off"
							name="phone"
							slotProps={{
								htmlInput: {
									maxLength: 8,
									pattern: "[0-9]*",
									inputMode: "numeric",
								},
								input: {
									startAdornment: (
										<InputAdornment
											position="start"
											sx={{
												color: "text.disabled",
												mr: 0,
												"& p": {
													fontSize: "inherit",
													lineHeight: "inherit",
												},
											}}
										>
											04
										</InputAdornment>
									),
									sx: {
										"& .MuiInputAdornment-root": {
											marginRight: 0,
										},
										"& input": {
											paddingLeft: 0,
										},
									},
								},
							}}
							onChange={handleChange}
							margin="normal"
							fullWidth
							value={formData.phone}
							required={true}
							onKeyPress={(event) => {
								// Only allow numeric values
								const char = event.key;
								const isNumeric = /^[0-9]+$/.test(char);
								if (!isNumeric || event.key === " ") {
									event.preventDefault();
								}
							}}
							error={formErrors.phone}
							helperText={
								formErrors.phone
									? "Please enter a valid phone number"
									: "Phone should start with 04 followed by 8 digits"
							}
						/>
					</Grid>
					{isPhoneExists && (
						<Grid container direction={"column"} alignItems={"start"}>
							<Button
								sx={{
									textTransform: "none",
									color: "white",
									float: "left",
									borderBottomStyle: "solid",
									borderBottomWidth: "1px",
									borderBottomColor: "white",
									fontSize: "12px",
									padding: "0px 5px",
									textAlign: "start",
								}}
								onClick={() => navigate({ to: "/patient/login" })}
							>
								Phone number already exists.
								<i
									style={{
										paddingLeft: "1px",
										color: "black",
										cursor: "pointer",
									}}
								>
									Click here to login
								</i>
							</Button>
						</Grid>
					)}
					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								Create a Password*
							</Typography>
						</Grid>
						<TextField
							sx={{ m: 0, ...inputStyle }}
							type="password"
							name="password"
							autoComplete="off"
							size="small"
							onChange={handleChange}
							fullWidth
							margin="normal"
							value={formData.password}
							error={formErrors.password}
							helperText={formErrors.password ? "Please enter a password" : ""}
						/>
					</Grid>
					<Grid container direction={"column"} alignItems={"start"}>
						<Grid sx={{ width: "100%" }}>
							<Typography
								sx={{
									fontSize: "14px",
									fontWeight: 400,
									color: "white",
									mb: 1,
								}}
								align="left"
							>
								Confirm your Password*
							</Typography>
						</Grid>
						<TextField
							sx={{ m: 0, ...inputStyle }}
							type="password"
							size="small"
							name="password_confirm"
							onChange={handleChange}
							autoComplete="off"
							fullWidth
							margin="normal"
							error={formErrors.password_confirm || !passwordIsMatching}
							helperText={
								formErrors.password_confirm
									? "Please enter a confirmation password"
									: passwordIsMatching
									? ""
									: "Your password don't match"
							}
						/>
						<FormControlLabel
							style={{ marginTop: "10px" }}
							control={
								<Checkbox
									size="small"
									checked={terms} // L'état 'checked' du Checkbox est contrôlé par notre state
									onChange={() => setTerms(!terms)}
									style={{ color: "white" }}
								/>
							}
							label={
								<span style={{ color: "#ffffff", fontSize: "0.7em" }}>
									I have read and accept the Terms & Conditions*
									<b style={{ marginLeft: "5px" }} onClick={goToTCs}>
										<i>Read it here</i>
									</b>
								</span>
							}
						/>
					</Grid>
				</Stack>
			),
		},
	];

	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Stack gap={1}>
					<Grid
						container
						direction={"column"}
						padding={"20px 0"}
						sx={{ borderRadius: "10px" }}
						boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
					>
						<Grid>
							<Typography
								sx={{
									fontSize: "38px",
									fontWeight: "bold",
									lineHeight: "1em",
									color: "green",
								}}
							>
								Pre-Screening
							</Typography>
						</Grid>
					</Grid>
					<Grid container sx={{ mt: 2 }}>
						<Button
							sx={{
								textTransform: "none",
								color: "#0000EE",
								fontSize: "12px",
								padding: "0px 5px",
							}}
							onClick={() => navigate({ to: "/patient/login" })}
						>
							Do you already have an account ? Click here to login
						</Button>
					</Grid>
					<Box>
						{!dataSubmitted && (
							<>
								<Box sx={{ backgroundColor: "green", borderRadius: 2, mt: 2 }}>{steps[0].content}</Box>
								<Divider style={{ marginTop: "10px" }}>
									<Typography sx={{ fontSize: "12px", color: "grey" }}>*Mandatory Field</Typography>
								</Divider>
								{canSubmit && passwordIsMatching && terms ? (
									<Button
										type="submit"
										fullWidth
										variant="contained"
										sx={{ mt: 2 }}
										onClick={handleSubmit}
									>
										Submit
									</Button>
								) : null}
							</>
						)}

						{dataSubmitted && (
							<>
								<Box
									sx={{
										border: "1px solid green",
										borderRadius: 2,
										mt: 2,
										p: 2,
									}}
								>
									<Grid
										container
										sx={{ width: "100%" }}
										alignItems={"center"}
										justifyContent={"center"}
										direction={"column"}
									>
										<Grid>
											<Typography
												sx={{
													color: "green",
													fontSize: "28px",
													fontWeight: "bold",
												}}
												align="center"
											>
												Thank you for <br />
												Signing Up
											</Typography>
										</Grid>
										<Grid>
											<CheckCircleIcon sx={{ color: "green", width: "100px", height: "100px" }} />
										</Grid>
										<Grid>
											<Typography sx={{ color: "grey" }}>
												You have successfully created your Zenith Clinics account.
											</Typography>
										</Grid>
										<Grid>
											<Button
												variant="contained"
												sx={{ mt: 2 }}
												onClick={() => navigate({ to: "/patient/phone-verification" })}
											>
												Start Pre-Screen
											</Button>
										</Grid>
									</Grid>
								</Box>
							</>
						)}
					</Box>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default FormRegister;